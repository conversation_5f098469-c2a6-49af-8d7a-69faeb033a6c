#!/usr/bin/env node

import { CreatePostBodyDtoInput } from '@malou-io/package-dto';
import { MediaType, PlatformKey, PostSource, PostType } from '@malou-io/package-utils';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import z from 'zod';
import { registerGetReviewsTool } from './tools/get-reviews.js';

// Create server instance
const server = new McpServer({
    name: 'malou-api',
    version: '1.0.0',
    capabilities: {
        resources: {},
        tools: {},
    },
});

interface MalouPost {
    _id: string;
    text?: string;
    language?: string;
    plannedPublicationDate: string;
    attachments?: string[];
    attachmentsName?: string;
    callToAction?: {
        actionType: string;
        url: string;
    };
    postTopic?: string;
    event?: {
        title: string;
        startDate: string;
        endDate: string;
    };
    offer?: {
        couponCode: string;
        onlineUrl: string;
        termsConditions: string;
    };
    hashtags?: {
        selected: any[];
        suggested: any[];
    };
    keys: string[];
    socialAttachments?: any[];
}

registerGetReviewsTool(server);

// Prepare post tool
server.tool(
    'prepare_post',
    'Prepare a post with content and settings',
    {
        post_id: z.string().describe('Post ID to prepare'),
        text: z.string().optional().describe('Post text content'),
        language: z.string().optional().describe('Post language (e.g., "fr", "en")'),
        planned_publication_date: z.string().optional().describe('Planned publication date (ISO string)'),
        attachments: z.array(z.string()).optional().describe('Array of media IDs to attach'),
        attachments_name: z.string().optional().describe('Name for the attachments'),
        call_to_action: z
            .object({
                action_type: z.string(),
                url: z.string(),
            })
            .optional()
            .describe('Call to action configuration'),
        post_topic: z.string().optional().describe('Post topic (e.g., "STANDARD")'),
        event: z
            .object({
                title: z.string(),
                start_date: z.string(),
                end_date: z.string(),
            })
            .optional()
            .describe('Event configuration'),
        offer: z
            .object({
                coupon_code: z.string(),
                online_url: z.string(),
                terms_conditions: z.string(),
            })
            .optional()
            .describe('Offer configuration'),
        keys: z.array(z.string()).optional().describe('Platform keys to publish to'),
        draft: z.boolean().optional().describe('Whether to save as draft (default: true)'),
    },
    async (params) => {
        try {
            const postPayload: any = {
                post: {
                    _hasInitWorkingPic: false,
                },
                keys: params.keys || ['gmb'],
                // draft: params.draft !== false, // Default to true
                draft: true,
            };

            // Add optional fields to post object
            if (params.text) postPayload.post.text = params.text;
            if (params.language) postPayload.post.language = params.language;
            if (params.planned_publication_date) postPayload.post.plannedPublicationDate = params.planned_publication_date;
            if (params.attachments) postPayload.post.attachments = params.attachments;
            if (params.attachments_name) postPayload.post.attachmentsName = params.attachments_name;
            if (params.call_to_action) {
                postPayload.post.callToAction = {
                    actionType: params.call_to_action.action_type,
                    url: params.call_to_action.url,
                };
            }
            if (params.post_topic) postPayload.post.postTopic = params.post_topic;
            if (params.event) {
                postPayload.post.event = {
                    title: params.event.title,
                    startDate: params.event.start_date,
                    endDate: params.event.end_date,
                };
            }
            if (params.offer) {
                postPayload.post.offer = {
                    couponCode: params.offer.coupon_code,
                    onlineUrl: params.offer.online_url,
                    termsConditions: params.offer.terms_conditions,
                };
            }

            // Add default hashtags and socialAttachments
            postPayload.post.hashtags = { selected: [], suggested: [] };
            postPayload.post.keys = [];
            postPayload.post.socialAttachments = [];

            const prepareUrl = `${MALOU_API_BASE}/posts/${params.post_id}/prepare`;
            const result: { data: MalouPost } | null = await makeMalouRequest<{ data: MalouPost }>(prepareUrl, {
                method: 'PUT',
                body: JSON.stringify(postPayload),
            });

            if (!result) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: 'Failed to prepare post',
                        },
                    ],
                };
            }

            return {
                content: [
                    {
                        type: 'text',
                        text: `Post prepared successfully!\nPost ID: ${result.data._id}\nText: ${result.data.text || 'No text'}\nLanguage: ${result.data.language || 'Not set'}\nKeys: ${result.data.keys.join(', ')}\nAttachments: ${result.data.attachments?.length || 0} media(s)`,
                    },
                ],
            };
        } catch (error) {
            return {
                content: [
                    {
                        type: 'text',
                        text: `Error preparing post: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    },
                ],
            };
        }
    }
);

async function main() {
    const transport = new StdioServerTransport();
    await server.connect(transport);
    console.error('Malou MCP Server running on stdio');
}

main().catch((error) => {
    console.error('Fatal error in main():', error);
    process.exit(1);
});
